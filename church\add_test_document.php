<?php
require_once 'config.php';

echo "Adding Test Document to <PERSON> Event\n";
echo "=====================================\n\n";

// Create a simple test PDF content
$pdfContent = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Emmanuel Outreach Event Details) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000274 00000 n 
0000000369 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
466
%%EOF";

// Create uploads directory if it doesn't exist
$uploadsDir = __DIR__ . '/uploads/events/';
if (!is_dir($uploadsDir)) {
    mkdir($uploadsDir, 0755, true);
}

// Create test PDF file
$fileName = 'emmanuel_event_details.pdf';
$uniqueName = 'event_35_' . time() . '_' . uniqid() . '.pdf';
$filePath = $uploadsDir . $uniqueName;

if (file_put_contents($filePath, $pdfContent)) {
    echo "Test PDF created: $filePath\n";
    
    // Insert into database
    $stmt = $pdo->prepare("
        INSERT INTO event_files (
            event_id, file_name, file_path, file_type, file_size, 
            file_category, is_header_banner, uploaded_by, upload_date
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $fileSize = filesize($filePath);
    $dbFilePath = '../uploads/events/' . $uniqueName;
    
    $result = $stmt->execute([
        35, // Emmanuel event ID
        $fileName,
        $dbFilePath,
        'application/pdf',
        $fileSize,
        'document', // Important: categorize as document
        0, // not header banner
        1 // admin user ID
    ]);
    
    if ($result) {
        echo "Document added to database successfully!\n";
        echo "File ID: " . $pdo->lastInsertId() . "\n";
        echo "File Name: $fileName\n";
        echo "File Path: $dbFilePath\n";
        echo "File Size: $fileSize bytes\n";
        echo "Category: document\n";
        
        echo "\nNow the Emmanuel event should show the Documents & Resources section in the user modal.\n";
    } else {
        echo "Error adding document to database!\n";
    }
} else {
    echo "Error creating test PDF file!\n";
}

// Verify the change
echo "\n" . str_repeat("=", 50) . "\n";
echo "Verification:\n";

$stmt = $pdo->prepare("
    SELECT id, file_name, file_category, file_type 
    FROM event_files 
    WHERE event_id = 35 
    ORDER BY upload_date DESC
");
$stmt->execute();
$files = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Emmanuel event now has " . count($files) . " files:\n";
foreach ($files as $file) {
    echo "  - {$file['file_name']} [{$file['file_category']}] ({$file['file_type']})\n";
}

// Test categorization
$promotionalMaterials = [];
$documents = [];

foreach ($files as $material) {
    if ($material['file_category'] === 'promotional') {
        $promotionalMaterials[] = $material;
    } else {
        $documents[] = $material;
    }
}

echo "\nCategorization for user modal:\n";
echo "Promotional Materials: " . count($promotionalMaterials) . "\n";
echo "Documents & Resources: " . count($documents) . "\n";

if (count($documents) > 0) {
    echo "\n✓ Documents & Resources section will now be visible in user modal!\n";
} else {
    echo "\n✗ Documents & Resources section will still be hidden.\n";
}
?>
