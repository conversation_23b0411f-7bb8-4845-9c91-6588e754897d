<?php
require_once 'config.php';

echo "Checking All PDF Files in Database\n";
echo "==================================\n\n";

// Find all PDF files
$stmt = $pdo->query("
    SELECT ef.*, e.title as event_title 
    FROM event_files ef 
    JOIN events e ON ef.event_id = e.id 
    WHERE ef.file_type = 'application/pdf' OR ef.file_name LIKE '%.pdf'
    ORDER BY ef.upload_date DESC
");
$pdfs = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Total PDF files found: " . count($pdfs) . "\n\n";

foreach ($pdfs as $pdf) {
    echo "PDF File Details:\n";
    echo "  Event: {$pdf['event_title']} (ID: {$pdf['event_id']})\n";
    echo "  File ID: {$pdf['id']}\n";
    echo "  Name: {$pdf['file_name']}\n";
    echo "  Category: '{$pdf['file_category']}'\n";
    echo "  Type: {$pdf['file_type']}\n";
    echo "  Path: {$pdf['file_path']}\n";
    echo "  Is Header Banner: " . ($pdf['is_header_banner'] ? 'YES' : 'NO') . "\n";
    echo "  Upload Date: {$pdf['upload_date']}\n";
    
    // Check if file exists
    $fullPath = __DIR__ . '/' . $pdf['file_path'];
    echo "  Full Path: $fullPath\n";
    echo "  File Exists: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
    
    // Try alternative path
    $altPath = str_replace('../', '', $pdf['file_path']);
    $altPath = __DIR__ . '/' . $altPath;
    echo "  Alt Path: $altPath\n";
    echo "  Alt Path Exists: " . (file_exists($altPath) ? 'YES' : 'NO') . "\n";
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

// Check uploads directory structure
echo "Checking uploads directory structure:\n";
echo "====================================\n";

$uploadsDir = __DIR__ . '/uploads/events/';
if (is_dir($uploadsDir)) {
    echo "Events uploads directory exists: $uploadsDir\n";
    
    // List all files in promotional directory
    $promotionalDir = $uploadsDir . 'promotional/';
    if (is_dir($promotionalDir)) {
        echo "\nPromotional directory contents:\n";
        $files = scandir($promotionalDir);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..') {
                $filePath = $promotionalDir . $file;
                $fileType = mime_content_type($filePath);
                $fileSize = filesize($filePath);
                echo "  - $file (Type: $fileType, Size: $fileSize bytes)\n";
            }
        }
    } else {
        echo "Promotional directory does not exist: $promotionalDir\n";
    }
    
    // List all files in main events directory
    echo "\nMain events directory contents:\n";
    $files = scandir($uploadsDir);
    foreach ($files as $file) {
        if ($file !== '.' && $file !== '..' && !is_dir($uploadsDir . $file)) {
            $filePath = $uploadsDir . $file;
            $fileType = mime_content_type($filePath);
            $fileSize = filesize($filePath);
            echo "  - $file (Type: $fileType, Size: $fileSize bytes)\n";
        }
    }
} else {
    echo "Events uploads directory does not exist: $uploadsDir\n";
}

// Check for Emmanuel event specifically
echo "\n" . str_repeat("=", 60) . "\n";
echo "Emmanuel Event File Analysis:\n";
echo "============================\n";

$stmt = $pdo->query("SELECT id, title FROM events WHERE title LIKE '%Emmanuel%' OR title LIKE '%Emmanel%'");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($events as $event) {
    echo "Event: {$event['title']} (ID: {$event['id']})\n";
    
    $stmt = $pdo->prepare("SELECT * FROM event_files WHERE event_id = ?");
    $stmt->execute([$event['id']]);
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($files as $file) {
        $filePath = str_replace('../', '', $file['file_path']);
        $fullPath = __DIR__ . '/' . $filePath;
        
        echo "  File: {$file['file_name']}\n";
        echo "    DB Type: {$file['file_type']}\n";
        echo "    DB Category: {$file['file_category']}\n";
        echo "    Path: $fullPath\n";
        
        if (file_exists($fullPath)) {
            $actualType = mime_content_type($fullPath);
            $actualSize = filesize($fullPath);
            echo "    Actual Type: $actualType\n";
            echo "    Actual Size: $actualSize bytes\n";
            
            // Check if it's actually a PDF
            if (strpos($actualType, 'pdf') !== false) {
                echo "    *** THIS IS ACTUALLY A PDF! ***\n";
            }
        } else {
            echo "    File does not exist!\n";
        }
        echo "\n";
    }
}
?>
