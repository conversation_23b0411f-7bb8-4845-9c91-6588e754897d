<?php
require_once 'config.php';

echo "Checking Event Files for Emmanuel Event\n";
echo "======================================\n\n";

// Find Emmanuel event
$stmt = $pdo->query("SELECT id, title FROM events WHERE title LIKE '%Emmanuel%' OR title LIKE '%Emmanel%'");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($events as $event) {
    echo "Event ID: {$event['id']}\n";
    echo "Event Title: {$event['title']}\n";
    echo str_repeat("-", 50) . "\n";
    
    // Get all files for this event
    $stmt = $pdo->prepare("
        SELECT id, file_name, file_path, file_type, file_size, file_category,
               is_header_banner, alt_text, display_order, upload_date
        FROM event_files
        WHERE event_id = ?
        ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
    ");
    $stmt->execute([$event['id']]);
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Total files found: " . count($files) . "\n\n";
    
    if (empty($files)) {
        echo "No files found for this event!\n\n";
        continue;
    }
    
    foreach ($files as $file) {
        echo "File Details:\n";
        echo "  ID: {$file['id']}\n";
        echo "  Name: {$file['file_name']}\n";
        echo "  Category: '{$file['file_category']}'\n";
        echo "  Type: {$file['file_type']}\n";
        echo "  Path: {$file['file_path']}\n";
        echo "  Is Header Banner: " . ($file['is_header_banner'] ? 'YES' : 'NO') . "\n";
        echo "  Upload Date: {$file['upload_date']}\n";
        
        // Check if file exists
        $fullPath = __DIR__ . '/' . $file['file_path'];
        echo "  Full Path: $fullPath\n";
        echo "  File Exists: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
        
        // Try alternative paths
        $altPath1 = __DIR__ . '/../' . ltrim($file['file_path'], '../');
        echo "  Alt Path 1: $altPath1\n";
        echo "  Alt Path 1 Exists: " . (file_exists($altPath1) ? 'YES' : 'NO') . "\n";
        
        $altPath2 = str_replace('../', '', $file['file_path']);
        $altPath2 = __DIR__ . '/' . $altPath2;
        echo "  Alt Path 2: $altPath2\n";
        echo "  Alt Path 2 Exists: " . (file_exists($altPath2) ? 'YES' : 'NO') . "\n";
        
        echo "\n";
    }
    
    // Categorize files like the user modal does
    echo "File Categorization (like user modal):\n";
    echo "=====================================\n";
    
    $promotionalMaterials = [];
    $documents = [];
    
    foreach ($files as $material) {
        if (!$material['is_header_banner']) {
            if ($material['file_category'] === 'promotional') {
                $promotionalMaterials[] = $material;
            } else {
                $documents[] = $material;
            }
        }
    }
    
    echo "Promotional Materials: " . count($promotionalMaterials) . "\n";
    foreach ($promotionalMaterials as $material) {
        echo "  - {$material['file_name']} (Category: '{$material['file_category']}')\n";
    }
    
    echo "\nDocuments & Resources: " . count($documents) . "\n";
    foreach ($documents as $document) {
        echo "  - {$document['file_name']} (Category: '{$document['file_category']}')\n";
    }
    
    echo "\n" . str_repeat("=", 60) . "\n\n";
}

// Check what categories exist in the database
echo "All File Categories in Database:\n";
echo "===============================\n";
$stmt = $pdo->query("SELECT DISTINCT file_category, COUNT(*) as count FROM event_files GROUP BY file_category");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($categories as $category) {
    echo "Category: '{$category['file_category']}' - Count: {$category['count']}\n";
}
?>
