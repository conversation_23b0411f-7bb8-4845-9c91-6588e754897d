<?php
require_once 'config.php';

echo "Checking User Password\n";
echo "=====================\n\n";

// Check <PERSON><PERSON>'s account
$stmt = $pdo->prepare("SELECT id, full_name, email, password_hash FROM members WHERE email = ?");
$stmt->execute(['<EMAIL>']);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if ($user) {
    echo "User found:\n";
    echo "ID: {$user['id']}\n";
    echo "Name: {$user['full_name']}\n";
    echo "Email: {$user['email']}\n";
    echo "Password hash: " . substr($user['password_hash'], 0, 20) . "...\n";

    // Try common passwords
    $commonPasswords = ['password', '123456', 'admin', 'test', 'godwin', 'bointa'];

    foreach ($commonPasswords as $password) {
        if (password_verify($password, $user['password_hash'])) {
            echo "\n✓ Password found: '$password'\n";
            break;
        }
    }
    
    // Set a known password for testing
    $newPassword = 'test123';
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("UPDATE members SET password_hash = ? WHERE id = ?");
    $result = $stmt->execute([$hashedPassword, $user['id']]);
    
    if ($result) {
        echo "\n✓ Password updated to: '$newPassword'\n";
        echo "You can now login with:\n";
        echo "Email: {$user['email']}\n";
        echo "Password: $newPassword\n";
    } else {
        echo "\n✗ Failed to update password\n";
    }
} else {
    echo "User not found!\n";
}
?>
