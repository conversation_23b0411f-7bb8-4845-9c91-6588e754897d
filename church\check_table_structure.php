<?php
require_once 'config.php';

echo "Checking Members Table Structure\n";
echo "===============================\n\n";

// Get table structure
$stmt = $pdo->query("DESCRIBE members");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Members table columns:\n";
foreach ($columns as $column) {
    echo "- {$column['Field']} ({$column['Type']})\n";
}

// Check a specific user
echo "\n" . str_repeat("=", 40) . "\n";
echo "Sample user data:\n";

$stmt = $pdo->prepare("SELECT * FROM members WHERE email = ? LIMIT 1");
$stmt->execute(['<EMAIL>']);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if ($user) {
    foreach ($user as $key => $value) {
        if (strlen($value) > 50) {
            $value = substr($value, 0, 50) . '...';
        }
        echo "$key: $value\n";
    }
} else {
    echo "User not found!\n";
}
?>
