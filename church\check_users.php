<?php
require_once 'config.php';

echo "Checking User Accounts\n";
echo "=====================\n\n";

// Get some user accounts
$stmt = $pdo->query("SELECT id, full_name, email, phone_number FROM members LIMIT 5");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Available user accounts:\n";
foreach ($users as $user) {
    echo "ID: {$user['id']}\n";
    echo "Name: {$user['full_name']}\n";
    echo "Email: {$user['email']}\n";
    echo "Phone: {$user['phone_number']}\n";
    echo str_repeat("-", 30) . "\n";
}

// Check if there's a test user
$stmt = $pdo->query("SELECT id, full_name, email, phone_number FROM members WHERE email LIKE '%test%' OR full_name LIKE '%test%' LIMIT 3");
$testUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (!empty($testUsers)) {
    echo "\nTest users found:\n";
    foreach ($testUsers as $user) {
        echo "ID: {$user['id']}\n";
        echo "Name: {$user['full_name']}\n";
        echo "Email: {$user['email']}\n";
        echo "Phone: {$user['phone_number']}\n";
        echo str_repeat("-", 30) . "\n";
    }
}
?>
