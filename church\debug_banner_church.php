<?php
require_once 'config.php';

echo "<h2>Debug from Church Directory</h2>";
echo "<p><strong>Script Name:</strong> " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "</p>";

// Test the fix_file_path_for_public function
$test_path = '../uploads/events/promotional/event_37_1752638595_0_Image_fx.jpg';
$fixed_path = fix_file_path_for_public($test_path);

echo "<p><strong>Original Path:</strong> " . $test_path . "</p>";
echo "<p><strong>Fixed Path:</strong> " . $fixed_path . "</p>";

// Check if the fixed path file exists
$full_path = __DIR__ . '/' . $fixed_path;
echo "<p><strong>Full Path:</strong> " . $full_path . "</p>";
echo "<p><strong>File Exists:</strong> " . (file_exists($full_path) ? 'YES' : 'NO') . "</p>";

// Test the actual event query
try {
    $stmt = $pdo->prepare("
        SELECT e.*,
               header_banner.file_path as header_banner_path,
               header_banner.file_type as header_banner_type,
               header_banner.file_name as header_banner_name,
               header_banner.alt_text as header_banner_alt,
               header_banner.is_header_banner
        FROM events e
        LEFT JOIN event_files header_banner ON e.id = header_banner.event_id
            AND header_banner.is_header_banner = 1
        WHERE e.id = 37 AND e.is_active = 1
    ");
    $stmt->execute();
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($event) {
        echo "<h3>Event Data:</h3>";
        echo "<p><strong>Title:</strong> " . htmlspecialchars($event['title']) . "</p>";
        echo "<p><strong>Header Banner Path:</strong> " . ($event['header_banner_path'] ?? 'NULL') . "</p>";
        echo "<p><strong>Header Banner Type:</strong> " . ($event['header_banner_type'] ?? 'NULL') . "</p>";
        
        if (!empty($event['header_banner_path'])) {
            $banner_path = fix_file_path_for_public($event['header_banner_path']);
            echo "<p><strong>Fixed Banner Path:</strong> " . $banner_path . "</p>";
            
            $full_banner_path = __DIR__ . '/' . $banner_path;
            echo "<p><strong>Full Banner Path:</strong> " . $full_banner_path . "</p>";
            echo "<p><strong>Banner File Exists:</strong> " . (file_exists($full_banner_path) ? 'YES' : 'NO') . "</p>";
            
            if (!empty($event['header_banner_type']) && strpos($event['header_banner_type'], 'image/') === 0) {
                echo "<h3>CSS Background Style:</h3>";
                echo "<code>background-image: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.7)), url('" . htmlspecialchars($banner_path) . "');</code>";
                
                echo "<h3>Image Preview:</h3>";
                if (file_exists($full_banner_path)) {
                    echo "<img src='" . htmlspecialchars($banner_path) . "' style='max-width: 400px; max-height: 200px; border: 1px solid #ccc;' alt='Banner Preview'>";
                } else {
                    echo "<p style='color: red;'>Image file not found at: " . $full_banner_path . "</p>";

                    // Try the correct path (uploads/ instead of ../uploads/)
                    $correct_path = str_replace('../uploads/', 'uploads/', $event['header_banner_path']);
                    $correct_full_path = __DIR__ . '/' . $correct_path;
                    echo "<p><strong>Trying correct path:</strong> " . $correct_path . "</p>";
                    echo "<p><strong>Correct full path:</strong> " . $correct_full_path . "</p>";
                    echo "<p><strong>Correct path exists:</strong> " . (file_exists($correct_full_path) ? 'YES' : 'NO') . "</p>";

                    if (file_exists($correct_full_path)) {
                        echo "<img src='" . htmlspecialchars($correct_path) . "' style='max-width: 400px; max-height: 200px; border: 1px solid #ccc;' alt='Banner Preview (Correct Path)'>";
                    }
                }
            }
        }
    } else {
        echo "<p>Event not found</p>";
    }
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage();
}
?>
