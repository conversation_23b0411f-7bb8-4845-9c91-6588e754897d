<?php
require_once 'config.php';

echo "Debugging <PERSON> Outreach Event Files\n";
echo "======================================\n\n";

// Find Emmanuel Outreach event
$stmt = $pdo->query("SELECT id, title FROM events WHERE title LIKE '%Emmanuel%' OR title LIKE '%Emmanel%'");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($events as $event) {
    echo "Event {$event['id']}: {$event['title']}\n";
    echo str_repeat("-", 50) . "\n";
    
    // Get all files for this event
    $stmt = $pdo->prepare("
        SELECT id, file_name, file_path, file_type, file_size, file_category,
               is_header_banner, alt_text, display_order, upload_date
        FROM event_files
        WHERE event_id = ?
        ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
    ");
    $stmt->execute([$event['id']]);
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Total files: " . count($files) . "\n\n";
    
    foreach ($files as $file) {
        echo "File ID {$file['id']}:\n";
        echo "  Name: {$file['file_name']}\n";
        echo "  Category: {$file['file_category']}\n";
        echo "  Type: {$file['file_type']}\n";
        echo "  Path: {$file['file_path']}\n";
        echo "  Header Banner: " . ($file['is_header_banner'] ? 'YES' : 'NO') . "\n";
        echo "  Upload Date: {$file['upload_date']}\n";
        echo "  File exists: " . (file_exists($file['file_path']) ? 'YES' : 'NO') . "\n";
        
        // Test fixed path
        $fixedPath = '../' . ltrim($file['file_path'], '../');
        echo "  Fixed path: $fixedPath\n";
        echo "  Fixed path exists: " . (file_exists($fixedPath) ? 'YES' : 'NO') . "\n";
        echo "\n";
    }
    
    // Simulate what get_event_details.php does
    echo "User-facing API simulation:\n";
    echo "===========================\n";
    
    $promotionalMaterials = [];
    $documents = [];
    
    foreach ($files as $material) {
        if (!$material['is_header_banner']) {
            if ($material['file_category'] === 'promotional') {
                $promotionalMaterials[] = $material;
            } else {
                $documents[] = $material;
            }
        }
    }
    
    echo "Promotional Materials: " . count($promotionalMaterials) . "\n";
    foreach ($promotionalMaterials as $material) {
        echo "  - {$material['file_name']} [{$material['file_category']}]\n";
    }
    
    echo "Documents: " . count($documents) . "\n";
    foreach ($documents as $document) {
        echo "  - {$document['file_name']} [{$document['file_category']}]\n";
    }
    
    echo "\n";
}

// Test the actual get_event_details.php API
echo "Testing actual get_event_details.php API:\n";
echo "========================================\n";

// Mock user session
session_start();
$_SESSION['user_id'] = 51;

// Find the Emmanuel event ID
$stmt = $pdo->query("SELECT id FROM events WHERE title LIKE '%Emmanuel%' OR title LIKE '%Emmanel%' LIMIT 1");
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if ($event) {
    $eventId = $event['id'];
    echo "Testing with event ID: $eventId\n";
    
    // Simulate the API call
    $url = "http://localhost/campaign/church/user/get_event_details.php?id=" . $eventId;
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    
    $data = json_decode($response, true);
    if ($data && $data['success']) {
        echo "API Success: YES\n";
        echo "Event Title: {$data['event']['title']}\n";
        
        // Check if HTML contains documents section
        $html = $data['html'];
        
        if (strpos($html, 'Documents & Resources') !== false) {
            echo "Documents & Resources section: FOUND\n";
        } else {
            echo "Documents & Resources section: NOT FOUND\n";
        }
        
        if (strpos($html, 'Promotional Materials') !== false) {
            echo "Promotional Materials section: FOUND\n";
        } else {
            echo "Promotional Materials section: NOT FOUND\n";
        }
        
        // Count download buttons
        $downloadCount = substr_count($html, 'btn-outline-primary');
        echo "Download buttons found: $downloadCount\n";
        
    } else {
        echo "API Success: NO\n";
        echo "Error: " . ($data['message'] ?? 'Unknown error') . "\n";
        echo "Raw response: " . substr($response, 0, 500) . "\n";
    }
}
?>
