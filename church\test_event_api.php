<?php
session_start();
require_once 'config.php';

// Simulate user login
$_SESSION['user_id'] = 51; // <PERSON><PERSON> Bo<PERSON>a
$_SESSION['user_name'] = '<PERSON><PERSON>';

echo "Testing Event Details API\n";
echo "=========================\n\n";

// Test Emmanuel event (ID 35)
echo "Testing Emmanuel Event (ID 35):\n";
echo "-------------------------------\n";

// Simulate the get_event_details.php logic
$eventId = 35;

$stmt = $pdo->prepare("
    SELECT e.*, ec.name as category_name
    FROM events e
    LEFT JOIN event_categories ec ON e.category_id = ec.id
    WHERE e.id = ?
");
$stmt->execute([$eventId]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    echo "Event not found!\n";
    exit;
}

echo "Event Title: {$event['title']}\n";
echo "Event Description: " . ($event['description'] ?: 'No description provided') . "\n";

// Get event materials
$eventMaterials = [];
$stmt = $pdo->prepare("
    SELECT id, file_name, file_path, file_type, file_size, file_category,
           is_header_banner, alt_text, display_order, upload_date
    FROM event_files
    WHERE event_id = ?
    ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
");
$stmt->execute([$eventId]);
$eventMaterials = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Total files: " . count($eventMaterials) . "\n";

// Separate materials by category
$promotionalMaterials = [];
$documents = [];

foreach ($eventMaterials as $material) {
    if (!$material['is_header_banner']) {
        if ($material['file_category'] === 'promotional') {
            $promotionalMaterials[] = $material;
        } else {
            $documents[] = $material;
        }
    }
}

echo "Promotional Materials: " . count($promotionalMaterials) . "\n";
echo "Documents & Resources: " . count($documents) . "\n";

// Test what would be shown in the modal
echo "\nWhat would be displayed in user modal:\n";
echo "=====================================\n";

if (!empty($promotionalMaterials)) {
    echo "✓ Promotional Materials section would be shown\n";
    foreach ($promotionalMaterials as $material) {
        echo "  - {$material['file_name']}\n";
    }
} else {
    echo "✗ Promotional Materials section would be hidden\n";
}

if (!empty($documents)) {
    echo "✓ Documents & Resources section would be shown\n";
    foreach ($documents as $document) {
        echo "  - {$document['file_name']}\n";
    }
} else {
    echo "✗ Documents & Resources section would be hidden\n";
}

echo "\n" . str_repeat("=", 50) . "\n\n";

// Test Summer Weekend event (ID 4) which has documents
echo "Testing Summer Weekend Event (ID 4):\n";
echo "------------------------------------\n";

$eventId = 4;

$stmt = $pdo->prepare("
    SELECT e.*, ec.name as category_name
    FROM events e
    LEFT JOIN event_categories ec ON e.category_id = ec.id
    WHERE e.id = ?
");
$stmt->execute([$eventId]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    echo "Event not found!\n";
    exit;
}

echo "Event Title: {$event['title']}\n";

// Get event materials
$eventMaterials = [];
$stmt = $pdo->prepare("
    SELECT id, file_name, file_path, file_type, file_size, file_category,
           is_header_banner, alt_text, display_order, upload_date
    FROM event_files
    WHERE event_id = ?
    ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
");
$stmt->execute([$eventId]);
$eventMaterials = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Total files: " . count($eventMaterials) . "\n";

// Separate materials by category
$promotionalMaterials = [];
$documents = [];

foreach ($eventMaterials as $material) {
    if (!$material['is_header_banner']) {
        if ($material['file_category'] === 'promotional') {
            $promotionalMaterials[] = $material;
        } else {
            $documents[] = $material;
        }
    }
}

echo "Promotional Materials: " . count($promotionalMaterials) . "\n";
echo "Documents & Resources: " . count($documents) . "\n";

// Test what would be shown in the modal
echo "\nWhat would be displayed in user modal:\n";
echo "=====================================\n";

if (!empty($promotionalMaterials)) {
    echo "✓ Promotional Materials section would be shown\n";
    foreach ($promotionalMaterials as $material) {
        echo "  - {$material['file_name']}\n";
    }
} else {
    echo "✗ Promotional Materials section would be hidden\n";
}

if (!empty($documents)) {
    echo "✓ Documents & Resources section would be shown\n";
    foreach ($documents as $document) {
        echo "  - {$document['file_name']}\n";
    }
} else {
    echo "✗ Documents & Resources section would be hidden\n";
}
?>
