<?php
require_once 'config.php';

echo "Testing User Modal for Events with Documents\n";
echo "===========================================\n\n";

// Test events that have PDF documents
$testEvents = [4, 33]; // Summer Weekend and Men's Gathering Talk

foreach ($testEvents as $eventId) {
    echo "Testing Event ID: $eventId\n";
    echo str_repeat("-", 30) . "\n";
    
    // Get event details
    $stmt = $pdo->prepare("SELECT title FROM events WHERE id = ?");
    $stmt->execute([$eventId]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event) {
        echo "Event not found!\n\n";
        continue;
    }
    
    echo "Event Title: {$event['title']}\n";
    
    // Get event materials (same query as get_event_details.php)
    $stmt = $pdo->prepare("
        SELECT id, file_name, file_path, file_type, file_size, file_category,
               is_header_banner, alt_text, display_order, upload_date
        FROM event_files
        WHERE event_id = ?
        ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
    ");
    $stmt->execute([$eventId]);
    $eventMaterials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Total files: " . count($eventMaterials) . "\n";
    
    // Separate materials by category (same logic as user modal)
    $promotionalMaterials = [];
    $documents = [];
    
    foreach ($eventMaterials as $material) {
        if (!$material['is_header_banner']) {
            if ($material['file_category'] === 'promotional') {
                $promotionalMaterials[] = $material;
            } else {
                $documents[] = $material;
            }
        }
    }
    
    echo "Promotional Materials: " . count($promotionalMaterials) . "\n";
    foreach ($promotionalMaterials as $material) {
        echo "  - {$material['file_name']} [{$material['file_category']}]\n";
    }
    
    echo "Documents & Resources: " . count($documents) . "\n";
    foreach ($documents as $document) {
        echo "  - {$document['file_name']} [{$document['file_category']}]\n";
        
        // Check if file exists
        $filePath = str_replace('../', '', $document['file_path']);
        $fullPath = __DIR__ . '/' . $filePath;
        echo "    Path: $fullPath\n";
        echo "    Exists: " . (file_exists($fullPath) ? 'YES' : 'NO') . "\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n\n";
}

// Now test what the actual user API returns
echo "Testing Actual User API Response:\n";
echo "=================================\n";

// Test with event 4 (Summer Weekend) which has PDF documents
$eventId = 4;
echo "Testing get_event_details.php for Event ID: $eventId\n";

// Simulate the API call by including the file logic
$stmt = $pdo->prepare("
    SELECT e.*, ec.name as category_name
    FROM events e
    LEFT JOIN event_categories ec ON e.category_id = ec.id
    WHERE e.id = ?
");
$stmt->execute([$eventId]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if ($event) {
    echo "Event found: {$event['title']}\n";
    
    // Get event materials
    $stmt = $pdo->prepare("
        SELECT id, file_name, file_path, file_type, file_size, file_category,
               is_header_banner, alt_text, display_order, upload_date
        FROM event_files
        WHERE event_id = ?
        ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
    ");
    $stmt->execute([$eventId]);
    $eventMaterials = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Separate materials by category
    $promotionalMaterials = [];
    $documents = [];
    
    foreach ($eventMaterials as $material) {
        if (!$material['is_header_banner']) {
            if ($material['file_category'] === 'promotional') {
                $promotionalMaterials[] = $material;
            } else {
                $documents[] = $material;
            }
        }
    }
    
    echo "Would show Promotional Materials section: " . (count($promotionalMaterials) > 0 ? 'YES' : 'NO') . "\n";
    echo "Would show Documents & Resources section: " . (count($documents) > 0 ? 'YES' : 'NO') . "\n";
    
    if (count($documents) > 0) {
        echo "\nDocuments that would be displayed:\n";
        foreach ($documents as $document) {
            // Test the fixFilePath function logic
            $originalPath = $document['file_path'];
            $fixedPath = str_replace('../', '', $originalPath);
            
            echo "  Document: {$document['file_name']}\n";
            echo "    Original path: $originalPath\n";
            echo "    Fixed path: $fixedPath\n";
            echo "    Full path: " . __DIR__ . "/$fixedPath\n";
            echo "    File exists: " . (file_exists(__DIR__ . "/$fixedPath") ? 'YES' : 'NO') . "\n";
            echo "\n";
        }
    }
} else {
    echo "Event not found!\n";
}
?>
